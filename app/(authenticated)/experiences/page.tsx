"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { ExperienceDetailModal } from "./components/experience-detail-modal"
import { DiscoveryTab } from "./components/discovery-tab"
import { UserBookingsTab } from "./components/user-bookings-tab"
import { PageLoading } from "@/components/page-loading"

function ExperiencesPageContent() {
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("discover")

  // Handle tab parameter from URL
  useEffect(() => {
    const tabParam = searchParams.get("tab")
    if (tabParam === "my-bookings") {
      setActiveTab("bookings")
    }
  }, [searchParams])

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Local Experiences</h1>
            <p className="text-muted-foreground mt-2">
              Discover unique activities and manage your bookings
            </p>
          </div>
        </div>
      </div>

      {/* Main Content with Tabs */}
      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 max-w-md">
            <TabsTrigger value="discover">Discover</TabsTrigger>
            <TabsTrigger value="bookings">My Bookings</TabsTrigger>
          </TabsList>

          <TabsContent value="discover" className="space-y-0">
            <DiscoveryTab />
          </TabsContent>

          <TabsContent value="bookings" className="space-y-0">
            <UserBookingsTab />
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <ExperienceDetailModal />
    </div>
  )
}

export default function ExperiencesPage() {
  return (
    <Suspense fallback={<PageLoading message="Loading experiences..." />}>
      <ExperiencesPageContent />
    </Suspense>
  )
}
