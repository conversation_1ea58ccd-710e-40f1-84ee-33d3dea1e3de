"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { useAuthStore } from "@/lib/domains/auth/auth.store"
import { PageLoading } from "@/components/page-loading"
import { useToast } from "@/components/ui/use-toast"
import { SubscriptionInitializer } from "@/components/subscription-initializer"
import { UserDataInitializer } from "@/components/user-data-initializer"
import { SettingsInitializer } from "@/components/settings-initializer"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"

export default function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuthStatus()
  const { toast } = useToast()
  const router = useRouter()
  const pathname = usePathname()

  // Get new user status from auth store (cached after first fetch)
  const { isNewUser } = useAuthStore()

  // Define routes that should NOT have header/sidebar
  const fullscreenRoutes = [
    "/complete-profile",
    "/welcome",
    "/admin", // Admin routes have their own layout
  ]

  // Check if current route should be fullscreen
  const isFullscreenRoute = fullscreenRoutes.some((route) => pathname.startsWith(route))

  useEffect(() => {
    // Only redirect if we're not loading and there's no user
    if (!loading && !user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to access this page.",
        variant: "warning",
      })
      router.push("/login")
    }
  }, [user, loading, router])

  // Handle new user experience routing (using cached data from auth store)
  useEffect(() => {
    // Skip if still loading auth or no user
    if (loading || !user?.uid) return

    // Skip new user check for admin routes (they have their own flow)
    if (pathname.startsWith("/admin")) return

    // Skip if new user status not yet determined (still loading or not fetched)
    if (isNewUser === null) return

    // Route based on cached new user status
    if (isNewUser === true && pathname !== "/welcome") {
      // New user trying to access any route other than welcome → redirect to welcome
      toast({
        title: "Welcome to Togeda!",
        description: "Let's get you started with a quick tour.",
        variant: "default",
      })
      router.push("/welcome")
    } else if (isNewUser === false && pathname === "/welcome") {
      // Existing user trying to access welcome → redirect to dashboard
      toast({
        title: "Welcome back!",
        description: "You've already completed the welcome experience.",
        variant: "default",
      })
      router.push("/dashboard")
    }
  }, [user, loading, isNewUser, pathname, router, toast])

  // Show loading state while checking authentication
  if (loading) {
    return <PageLoading message="Checking authentication..." />
  }

  // Don't render children until we confirm the user is authenticated
  if (!user) {
    return null
  }

  // User is authenticated, render the children with domain-specific initializers
  return (
    <>
      {/* Initialize domain-specific stores */}
      <UserDataInitializer />
      <SubscriptionInitializer />
      <SettingsInitializer />

      {isFullscreenRoute ? (
        // Fullscreen layout for special pages
        children
      ) : (
        // Standard layout with header and sidebar
        <div className="min-h-screen">
          <AppHeader />
          <div className="pt-16 flex">
            <AppSidebar />
            <main className="flex-1">{children}</main>
          </div>
        </div>
      )}
    </>
  )
}
