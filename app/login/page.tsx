"use client"

import { Suspense } from "react"
import { ProviderSignInForm } from "./components/provider-sign-in-form"
import { PageLoading } from "@/components/page-loading"
import { useAuthRedirect } from "@/lib/domains/auth/auth.hooks"

function LoginPageContent() {
  const { loading, isRedirecting } = useAuthRedirect()

  // Show loading while checking auth state or redirecting
  if (loading || isRedirecting) {
    return <PageLoading message="Loading..." />
  }

  return <ProviderSignInForm />
}

export default function LoginPage() {
  return (
    <Suspense fallback={<PageLoading message="Loading..." />}>
      <LoginPageContent />
    </Suspense>
  )
}
