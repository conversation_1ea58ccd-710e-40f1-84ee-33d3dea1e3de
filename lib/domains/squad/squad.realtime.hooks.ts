"use client"

import { useEffect, useCallback, useState } from "react"
import { SquadRealtimeService } from "./squad.realtime.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useSquadStore } from "./squad.store"
import { SquadMember } from "./squad.types"

/**
 * Hook to get real-time updates for a specific squad
 */
export const useRealtimeSquad = (squadId: string) => {
  // Use local state for loading and error to avoid shared state issues
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Use store for the squad data
  const currentSquad = useSquadStore((state) => state.currentSquad)
  const setCurrentSquad = useCallback((squad: any) => {
    useSquadStore.setState({ currentSquad: squad })
  }, [])

  useEffect(() => {
    if (!squadId) {
      setCurrentSquad(null)
      setLoading(false)
      setError(null)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = SquadRealtimeService.subscribeToSquad(squadId, (data, err) => {
      if (err) {
        console.error("Error getting real-time squad:", err)
        setError(err)
        setLoading(false)
        return
      }

      setCurrentSquad(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [squadId, setCurrentSquad])

  return { squad: currentSquad, loading, error }
}

/**
 * Hook to get real-time updates for user squads
 */
export const useRealtimeUserSquads = () => {
  const user = useUser()

  // Use local state for loading and error to avoid shared state issues
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Use store for the squads data
  const squads = useSquadStore((state) => state.squads)
  const setSquads = useCallback((data: any[]) => {
    useSquadStore.setState({ squads: data })
  }, [])
  const clearSquads = useCallback(() => {
    useSquadStore.getState().clearSquads()
  }, [])

  useEffect(() => {
    // If no user, clear squads and reset state
    if (!user?.uid) {
      clearSquads()
      setLoading(false)
      setError(null)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = SquadRealtimeService.subscribeToUserSquads(user.uid, (data, err) => {
      if (err) {
        console.error("Error getting real-time user squads:", err)
        setError(err)
        setLoading(false)
        return
      }

      setSquads(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [user?.uid, setSquads, clearSquads])

  return { squads, loading, error }
}

/**
 * Hook to get real-time updates for squad members
 */
export const useRealtimeSquadMembers = (squadId: string) => {
  // Use local state for loading and error to avoid shared state issues
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Use store for the squad members data
  const squadMembers = useSquadStore((state) => state.squadMembers)
  const setSquadMembers = useCallback((data: SquadMember[]) => {
    useSquadStore.setState({ squadMembers: data })
  }, [])

  useEffect(() => {
    if (!squadId) {
      setSquadMembers([])
      setLoading(false)
      setError(null)
      return
    }

    setLoading(true)
    setError(null)

    const unsubscribe = SquadRealtimeService.subscribeToSquadMembers(squadId, (data, err) => {
      if (err) {
        console.error("Error getting real-time squad members:", err)
        setError(err)
        setLoading(false)
        return
      }

      setSquadMembers(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [squadId, setSquadMembers])

  return { members: squadMembers, loading, error }
}
