import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
  writeBatch,
  increment,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  Squad,
  SquadCreateData,
  SquadUpdateData,
  SquadMember,
  UserSquad,
  SquadMemberCreateData,
  UserSquadCreateData,
} from "./squad.types"

/**
 * Squad service for Firebase operations
 */
export class SquadService {
  private static readonly COLLECTION = "squads"

  /**
   * Create a new squad
   * @param squadData Squad data
   * @returns The new squad ID
   */
  static async createSquad(squadData: SquadCreateData): Promise<string> {
    try {
      const squadRef = doc(collection(db, this.COLLECTION))
      const squadId = squadRef.id
      const batch = writeBatch(db)

      // Create squad document
      batch.set(squadRef, {
        ...squadData,
        id: squadId,
        memberCount: 1, // Leader is the first member
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // Add leader as a member in squad subcollection
      const leaderMemberRef = doc(db, `${this.COLLECTION}/${squadId}/members/${squadData.leaderId}`)
      batch.set(leaderMemberRef, {
        userId: squadData.leaderId,
        role: "leader",
        joinedAt: serverTimestamp(),
        joinMethod: "legacy_invitation",
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      } as SquadMemberCreateData & { createdAt: any; updatedAt: any })

      // Add squad to user's squads subcollection
      const userSquadRef = doc(db, `users/${squadData.leaderId}/squads/${squadId}`)
      batch.set(userSquadRef, {
        squadId,
        squadName: squadData.name,
        role: "leader",
        joinedAt: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      } as UserSquadCreateData & { createdAt: any; updatedAt: any })

      await batch.commit()
      return squadId
    } catch (error) {
      console.error("Error creating squad:", error)
      throw error
    }
  }

  /**
   * Get a squad by ID
   * @param squadId Squad ID
   * @returns The squad data or null if not found
   */
  static async getSquad(squadId: string): Promise<Squad | null> {
    try {
      const squadDoc = await getDoc(doc(db, this.COLLECTION, squadId))

      if (squadDoc.exists()) {
        return { ...squadDoc.data(), id: squadId } as Squad
      }

      return null
    } catch (error) {
      console.error("Error getting squad:", error)
      throw error
    }
  }

  /**
   * Get squads for a user
   * @param userId User ID
   * @returns Array of squads
   */
  static async getUserSquads(userId: string): Promise<Squad[]> {
    try {
      // Query user's squad subcollection
      const userSquadsRef = collection(db, `users/${userId}/squads`)
      const userSquadsSnapshot = await getDocs(userSquadsRef)

      if (userSquadsSnapshot.empty) {
        return []
      }

      // Get squad details for each user squad
      const squads: Squad[] = []
      for (const userSquadDoc of userSquadsSnapshot.docs) {
        const userSquad = userSquadDoc.data() as UserSquad
        const squadDoc = await getDoc(doc(db, this.COLLECTION, userSquad.squadId))

        if (squadDoc.exists()) {
          squads.push({ ...squadDoc.data(), id: squadDoc.id } as Squad)
        }
      }

      return squads
    } catch (error) {
      console.error("Error getting user squads:", error)
      throw error
    }
  }

  /**
   * Update a squad
   * @param squadId Squad ID
   * @param squadData Squad data to update
   * @returns Service response
   */
  static async updateSquad(squadId: string, squadData: SquadUpdateData): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, squadId), {
        ...squadData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating squad:", error)
      return { success: false, error }
    }
  }

  /**
   * Get squad members
   * @param squadId Squad ID
   * @returns Array of squad members
   */
  static async getSquadMembers(squadId: string): Promise<SquadMember[]> {
    try {
      const membersRef = collection(db, `${this.COLLECTION}/${squadId}/members`)
      const membersSnapshot = await getDocs(membersRef)

      return membersSnapshot.docs.map(
        (doc) =>
          ({
            ...doc.data(),
            id: doc.id,
          }) as SquadMember
      )
    } catch (error) {
      console.error("Error getting squad members:", error)
      throw error
    }
  }

  /**
   * Add a member to a squad
   * @param squadId Squad ID
   * @param userId User ID to add
   * @returns Service response
   */
  static async addMember(squadId: string, userId: string): Promise<ServiceResponse> {
    try {
      // Get squad details for the user squad document
      const squadDoc = await getDoc(doc(db, this.COLLECTION, squadId))
      if (!squadDoc.exists()) {
        return { success: false, error: new Error("Squad not found") }
      }

      const squad = squadDoc.data() as Squad
      const batch = writeBatch(db)

      // Add member to squad subcollection
      const memberRef = doc(db, `${this.COLLECTION}/${squadId}/members/${userId}`)
      batch.set(memberRef, {
        userId,
        role: "member",
        joinedAt: serverTimestamp(),
        joinMethod: "legacy_invitation",
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      } as SquadMemberCreateData & { createdAt: any; updatedAt: any })

      // Add squad to user's squads subcollection
      const userSquadRef = doc(db, `users/${userId}/squads/${squadId}`)
      batch.set(userSquadRef, {
        squadId,
        squadName: squad.name,
        role: "member",
        joinedAt: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      } as UserSquadCreateData & { createdAt: any; updatedAt: any })

      // Increment member count
      const squadRef = doc(db, this.COLLECTION, squadId)
      batch.update(squadRef, {
        memberCount: increment(1),
        updatedAt: serverTimestamp(),
      })

      await batch.commit()
      return { success: true }
    } catch (error) {
      console.error("Error adding member to squad:", error)
      return { success: false, error }
    }
  }

  /**
   * Add a member to a squad with join tracking
   * @param squadId Squad ID
   * @param userId User ID to add
   * @param userEmail User email
   * @param userName User name
   * @param joinMethod How the user joined
   * @param invitationId Optional invitation ID
   * @param invitedBy Optional inviter user ID
   * @returns Service response
   */
  static async addMemberWithTracking(
    squadId: string,
    userId: string,
    userEmail: string,
    userName: string,
    joinMethod: "email_invitation" | "shareable_link",
    invitationId?: string,
    invitedBy?: string
  ): Promise<ServiceResponse> {
    try {
      // Add member to squad
      const addResult = await this.addMember(squadId, userId)
      if (!addResult.success) {
        return addResult
      }

      // Track the join
      const { SquadMemberJoinService } = await import("./squad-member-join.service")
      await SquadMemberJoinService.trackMemberJoin({
        squadId,
        userId,
        userEmail,
        userName,
        joinMethod,
        invitationId,
        invitedBy,
      })

      // Send email notification to squad leader
      try {
        // Get squad details to get leader info
        const squad = await this.getSquad(squadId)
        if (squad) {
          // Get squad leader's user data
          const { UserService } = await import("../user/user.service")
          const squadLeader = await UserService.getUser(squad.leaderId)
          console.log("Sending email to squad leader:", squadLeader)
          if (squadLeader?.email) {
            // Use the client-side email service
            const { sendSingleEmail } = await import("@/lib/email-service")

            // Get member count
            const members = await this.getSquadMembers(squadId)
            const memberCount = members.length

            // Generate squad dashboard link
            const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
            const squadLink = `${baseUrl}/squads/${squadId}`

            // Prepare template parameters
            const templateParams = {
              username: squadLeader.displayName || squadLeader.email,
              squadName: squad.name,
              newMemberName: userName,
              newMemberEmail: userEmail,
              joinMethod: joinMethod === "email_invitation" ? "Direct Invite" : "Shareable Link",
              memberCount,
              squadLink,
              joinDate: new Date().toLocaleDateString(),
            }

            // Send email using client-side service (template ID 170)
            const emailResult = await sendSingleEmail({
              to: squadLeader.email,
              templateId: 170, // SQUAD_MEMBER_JOINED template
              params: templateParams,
            })

            if (emailResult.success) {
              console.log("Squad leader notification sent successfully:", {
                squadId,
                squadName: squad.name,
                newMemberName: userName,
                squadLeaderEmail: squadLeader.email,
                messageId: emailResult.messageId,
              })
            } else {
              console.warn("Failed to send squad leader notification:", emailResult.error)
            }
          } else {
            console.warn("Squad leader email not found, skipping notification:", {
              squadId,
              leaderId: squad.leaderId,
            })
          }
        }
      } catch (notificationError) {
        // Don't fail the join if notification fails
        console.warn("Failed to send squad leader notification:", notificationError)
      }

      // Update invitation send status if this was from an email invitation
      if (joinMethod === "email_invitation" || joinMethod === "shareable_link") {
        try {
          const { InvitationSendService } = await import("../invitation/invitation-send.service")

          // Handle invitation-send tracking only for email invitations
          if (invitationId && joinMethod === "email_invitation") {
            // Find invitation sends for this user's email and invitation
            const invitationSends = await InvitationSendService.getInvitationSends(
              squadId,
              invitationId
            )

            // Find the send record for this user's email
            const userSend = invitationSends.find(
              (send) => send.email.toLowerCase() === userEmail.toLowerCase()
            )

            if (userSend && userSend.status === "sent") {
              // Update existing invitation send to accepted
              await InvitationSendService.updateInvitationSendStatus(
                squadId,
                userSend.id,
                "accepted"
              )
              console.log("Updated invitation send status to accepted:", {
                squadId,
                invitationSendId: userSend.id,
                userEmail,
                joinMethod,
              })
            } else if (userSend && userSend.status !== "sent") {
              console.log("Invitation send already processed:", {
                squadId,
                invitationSendId: userSend.id,
                userEmail,
                currentStatus: userSend.status,
              })
            }
            // Note: If no userSend found, it means this user wasn't specifically invited
            // but is joining via a generic link - no invitation-send tracking needed
          }
          // For generic link joins (joinMethod === "shareable_link"), no invitation-send tracking needed
        } catch (err) {
          // Don't fail the join if invitation send tracking fails
          console.warn("Failed to update invitation send status:", err)
        }
      }

      return { success: true }
    } catch (error) {
      console.error("Error adding member with tracking:", error)
      return { success: false, error }
    }
  }

  /**
   * Remove a member from a squad
   * @param squadId Squad ID
   * @param userId User ID to remove
   * @returns Service response
   */
  static async removeMember(squadId: string, userId: string): Promise<ServiceResponse> {
    try {
      const batch = writeBatch(db)

      // Remove member from squad subcollection
      const memberRef = doc(db, `${this.COLLECTION}/${squadId}/members/${userId}`)
      batch.delete(memberRef)

      // Remove squad from user's squads subcollection
      const userSquadRef = doc(db, `users/${userId}/squads/${squadId}`)
      batch.delete(userSquadRef)

      // Decrement member count
      const squadRef = doc(db, this.COLLECTION, squadId)
      batch.update(squadRef, {
        memberCount: increment(-1),
        updatedAt: serverTimestamp(),
      })

      await batch.commit()
      return { success: true }
    } catch (error) {
      console.error("Error removing member from squad:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user is the squad leader
   * @param userId User ID
   * @param squadId Squad ID
   * @returns True if the user is the squad leader
   */
  static async isUserSquadLeader(userId: string, squadId: string): Promise<boolean> {
    try {
      const squad = await this.getSquad(squadId)
      return squad?.leaderId === userId
    } catch (error) {
      console.error("Error checking if user is squad leader:", error)
      return false
    }
  }

  /**
   * Check if a user is a member of a squad
   * @param userId User ID
   * @param squadId Squad ID
   * @returns True if the user is a member of the squad
   */
  static async isUserSquadMember(userId: string, squadId: string): Promise<boolean> {
    try {
      const memberDoc = await getDoc(doc(db, `${this.COLLECTION}/${squadId}/members/${userId}`))
      return memberDoc.exists()
    } catch (error) {
      console.error("Error checking if user is squad member:", error)
      return false
    }
  }

  /**
   * Delete a squad
   * @param squadId Squad ID
   * @returns Service response
   */
  static async deleteSquad(squadId: string): Promise<ServiceResponse> {
    try {
      // Check if squad exists
      const squad = await this.getSquad(squadId)
      if (!squad) {
        return { success: false, error: new Error("Squad not found") }
      }

      // Delete the squad
      await deleteDoc(doc(db, this.COLLECTION, squadId))

      // TODO: In a production app, we would also:
      // 1. Delete or archive all related trips
      // 2. Delete or archive all related invitations
      // 3. Possibly notify all members
      // This would typically be handled by a Cloud Function or backend service

      return { success: true }
    } catch (error) {
      console.error("Error deleting squad:", error)
      return { success: false, error }
    }
  }
}
