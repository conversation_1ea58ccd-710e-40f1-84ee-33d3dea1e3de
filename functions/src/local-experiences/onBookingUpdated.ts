import * as functions from "firebase-functions"
import * as admin from "firebase-admin"
import { EmailService } from "../utils/email.service"

/**
 * Interface for experience booking data
 */
interface ExperienceBooking {
  id: string
  experienceId: string
  experienceTitle: string
  experienceLocation: string
  experienceHost: string
  userId: string
  userEmail: string
  userName: string
  date: string
  time: string
  availabilityId: string
  guests: number
  guestDetails?: Array<{
    name: string
    email?: string
    phone?: string
  }>
  specialRequests?: string
  pricing: {
    basePrice: number
    guests: number
    subtotal: number
    taxes: number
    fees: number
    total: number
    currency: string
  }
  status: "pending" | "confirmed" | "cancelled" | "completed"
  paymentStatus: "pending" | "paid" | "failed" | "refunded"
  stripeSessionId?: string
  stripePaymentIntentId?: string
  stripeCustomerId?: string
  bookedAt: admin.firestore.Timestamp
  confirmedAt?: admin.firestore.Timestamp
  cancelledAt?: admin.firestore.Timestamp
  completedAt?: admin.firestore.Timestamp
  cancellationReason?: string
}

/**
 * Interface for local experience data
 */
interface LocalExperience {
  id: string
  title: string
  description: string
  shortDescription: string
  host: {
    name: string
    avatar?: string
    responseTime: string
    languages: string[]
    bio: string
    email?: string
    phone?: string
    internalHostEmail?: string
  }
  location: {
    address: string
    city: string
    state: string
    country: string
    zipCode: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  pricing: {
    basePrice: number
    currency: string
    priceType: "per_person" | "per_group"
  }
  duration: number
  maxGuests: number
  minGuests: number
  categories: string[]
  images: string[]
  inclusions: Array<{
    item: string
    included: boolean
  }>
  cancellationPolicy: string
  rating: number
  reviewCount: number
  isActive: boolean
  stripeProductId?: string
  bookingModel?: "per_session" | "per_max_guest"
  createdAt: admin.firestore.Timestamp
}

/**
 * Firebase Function that triggers when a booking document is updated
 *
 * Specifically handles the booking confirmation flow:
 * 1. Booking is initially created with status "pending" and paymentStatus "pending"
 * 2. After successful Stripe payment, the booking is updated to status "confirmed" and paymentStatus "paid"
 * 3. This function detects that status change and sends email notifications to both guest and host
 *
 * Email notifications are only sent when:
 * - The booking status changes from non-confirmed to confirmed
 * - The payment status is "paid"
 *
 * This ensures emails are sent exactly once when payment is completed.
 */
export const onBookingUpdated = functions.firestore
  .document("localExperiences/{experienceId}/bookings/{bookingId}")
  .onUpdate(
    async (
      change: functions.Change<functions.firestore.QueryDocumentSnapshot>,
      context: functions.EventContext
    ) => {
      try {
        const { experienceId, bookingId } = context.params
        const beforeData = change.before.data() as ExperienceBooking
        const afterData = change.after.data() as ExperienceBooking

        console.log(`Booking updated: ${bookingId} for experience ${experienceId}`)

        // Only send emails when booking status changes from pending to confirmed
        const wasConfirmed =
          beforeData.status === "confirmed" && beforeData.paymentStatus === "paid"
        const isNowConfirmed =
          afterData.status === "confirmed" && afterData.paymentStatus === "paid"

        if (wasConfirmed || !isNowConfirmed) {
          console.log(
            `Booking ${bookingId} - no email needed. Was confirmed: ${wasConfirmed}, Is now confirmed: ${isNowConfirmed}`
          )
          return {
            success: true,
            message: "No email notification needed for this update",
            bookingId,
            experienceId,
          }
        }

        console.log(`Booking ${bookingId} confirmed after payment - sending email notifications`)

        // Get the experience details
        const db = admin.firestore()
        const experienceDoc = await db.collection("localExperiences").doc(experienceId).get()

        if (!experienceDoc.exists) {
          console.error(`Experience document not found for experienceId: ${experienceId}`)
          return {
            success: false,
            error: "Experience document not found",
            bookingId,
            experienceId,
          }
        }

        const experienceData = experienceDoc.data() as LocalExperience

        console.log(`Processing email notifications for booking ${bookingId}`)

        // Prioritize guestDetails email over userEmail for contact information
        const primaryGuestEmail = afterData.guestDetails?.[0]?.email || afterData.userEmail
        const primaryGuestName = afterData.guestDetails?.[0]?.name || afterData.userName
        const primaryGuestPhone = afterData.guestDetails?.[0]?.phone || "Not provided"

        // Prepare booking details for emails
        const bookingDetails = {
          bookingId,
          guestName: primaryGuestName,
          guestEmail: primaryGuestEmail,
          guestPhone: primaryGuestPhone,
          experienceTitle: experienceData.title,
          hostName: experienceData.host.name,
          hostEmail: experienceData.host.email,
          hostPhone: experienceData.host.phone,
          bookingDate: afterData.date,
          bookingTime: afterData.time,
          guestCount: afterData.guests,
          totalAmount: afterData.pricing.total,
          specialRequests: afterData.specialRequests || "None",
          experienceLocation: experienceData.location.address,
          experienceDescription: experienceData.shortDescription,
          cancellationPolicy: experienceData.cancellationPolicy,
        }

        // Send confirmation email to guest (prioritize guestDetails email)
        const guestEmailResult = await EmailService.sendBookingConfirmationEmail(
          primaryGuestEmail,
          bookingDetails
        )

        const auditEmailTo = process.env.AUDIT_EMAIL_TO || "<EMAIL>"
        const auditEmailResult = await EmailService.sendBookingConfirmationEmail(
          auditEmailTo,
          bookingDetails
        )

        // Send notification email to host (use internal email if available, fallback to public email)
        const hostEmail = experienceData.host.internalHostEmail || experienceData.host.email
        let hostEmailResult: { success: boolean; messageId?: string; error?: string } = {
          success: false,
          error: "No host email configured",
        }

        if (hostEmail) {
          hostEmailResult = await EmailService.sendHostNotificationEmail(hostEmail, {
            ...bookingDetails,
            hostName: experienceData.host.name,
          })
        } else {
          console.warn(`No host email found for experience ${experienceId}`)
        }

        console.log("Email notification results:", {
          auditEmail: auditEmailResult,
          guestEmail: guestEmailResult,
          hostEmail: hostEmailResult,
        })

        return {
          success: true,
          bookingId,
          experienceId,
          guestName: primaryGuestName,
          experienceTitle: experienceData.title,
          emailResults: {
            guestEmailSent: guestEmailResult.success,
            hostEmailSent: hostEmailResult.success,
            guestEmailError: guestEmailResult.error,
            hostEmailError: hostEmailResult.error,
          },
        }
      } catch (error) {
        console.error("Error in onBookingUpdated function:", error)

        // Don't throw the error to prevent function retries
        // Log the error and return a failure response
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          bookingId: context.params.bookingId,
          experienceId: context.params.experienceId,
        }
      }
    }
  )
